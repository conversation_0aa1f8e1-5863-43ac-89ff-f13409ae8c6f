#region USING DECLARATIONS
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Runtime.CompilerServices;	// For MemberCallerName
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

#region UPDATE LOG
/// *******		Moved DrawSeenSignals() after check for CurrentBar > OptimizeBars, some logging
/// *******		Added option to NOT use the ind-calculated dQtyPerX of contracts
/// *******		Changed Quantity remaining calculation for partial exits
#endregion


// This namespace holds Strategies in this folder and is required. Do not change it. 
namespace NinjaTrader.NinjaScript.Strategies
{
	public class Strike123Mngd : Strategy
	{
		private const string	VERSION = "*******";	

		#region GLOBALS
		public enum OptMode
		{
			Simplified,
			Standard,
			Exhaustive
		}

		public enum ReOptMode
		{
			IntervalInBars,
			WhenFlat,
			IntervalInBarsWhenFlat
		}

		private bool			tradeLong			= true;
		private bool			tradeShort			= true;
		private bool			dailyLimitHit		= false;
		
		private double 			dailyProfit			= 0;
		private double 			stopPrice			= 0;

		private	int 			exitQty				= 1;
		private	int 			numExits			= 0;
		private	int 			qtyLeft				= 0;
		private	int				exitsDone			= 0;
		private int				activeSessionNum	= 0;

		private bool			inSession			= true;
		private bool			inSessionChanged	= false;
		private bool			moveToBEDone		= false;
		
		private string			lastDashboard		= "";
		private string			lastCaller			= "";
		private string			lastLogMsg			= "";
		private string			lastLogTime;
		private string			chartInstrument;
		private string			logToFileName		= "";
		private string			logPath				= "";

		protected DateTime		startTime1, startTime2, startTime3, startTime4, startTime5;
		protected DateTime		endTime1, endTime2, endTime3, endTime4, endTime5;
		protected DateTime		closeTime, closeTime1, closeTime2, closeTime3, closeTime4, closeTime5;

		private System.Windows.Controls.Button		shortButton, longButton, exitButton;
		private System.Windows.Controls.Grid		myGrid;
		
		private	NinjaTrader.NinjaScript.Indicators.TradingIndicators.OneTwoThreeStrikeAI		strike123;
		#endregion
		
		protected override void OnStateChange()
		{
			if (State == State.SetDefaults)
			{
				Description									= @"Strategy to trade 1-2-3 Strike indicator";
				Name										= "Strike123Mngd";
				LogToFileName								= Name;
				Calculate									= Calculate.OnBarClose;
				EntriesPerDirection							= 1;
				EntryHandling								= EntryHandling.AllEntries;
				IsExitOnSessionCloseStrategy				= false;
				ExitOnSessionCloseSeconds					= 30;
				IsFillLimitOnTouch							= false;
				MaximumBarsLookBack							= MaximumBarsLookBack.Infinite;
				OrderFillResolution							= OrderFillResolution.Standard;
				Slippage									= 0;
				StartBehavior								= StartBehavior.WaitUntilFlat;
				TimeInForce									= TimeInForce.Gtc;
				TraceOrders									= false;
				RealtimeErrorHandling						= RealtimeErrorHandling.StopCancelClose;
				StopTargetHandling							= StopTargetHandling.PerEntryExecution;
				BarsRequiredToTrade							= 20;
				// Disable this property for performance gains in Strategy Analyzer optimizations
				// See the Help Guide for additional information
				IsInstantiatedOnEachOptimizationIteration	= true;
				
				// TimeZone
				TimeZoneOffset			= -3;
				
				// Entry
				TradeLong				= true;
				TradeShort				= true;
				UseRisk					= true;
				RiskAmount				= 1000;
				QuantityL				= 1;
				QuantityS				= 1;
				MaxContracts			= 50;
				MinPF					= 2.0;
				MinWinRateL				= 0;
				MinWinRateS				= 0;
				
				// Move to BreakEven
				UseBreakeven			= true;
				BE_ActivTicks			= 16;
				BE_Offset				= 1;
				
				// Exits
				FlattenEoS				= false;
				UseTrailingStop			= true;
				UsePartialExits			= true;
				NumExitsL				= 4;
				NumExitsS				= 4;
				ExitOnVolume			= false;
				AutoExitQty				= true;
				PartXQtyL				= 1;
				PartXQtyS				= 1;
				
				// Daily Targets
				DailyMinTarget			= 0;
				DailyMaxLoss			= 0;

				// Indicator
				OptimizeBars			= 1000;
				OptimizationMode		= OptMode.Standard;
				ReOptAuto				= true;
				ReOptimizationMode		= ReOptMode.WhenFlat;
				ReOptimizeBars			= 30;

				// Trading session days / hours
				TradeSunday				= true;
				TradeMonday				= true;
				TradeTuesday			= true;
				TradeWednesday			= true;
				TradeThursday			= true;
				TradeFriday				= true;

				UseSession1				= false;
				StartTime1				= DateTime.Parse("10:00 AM", System.Globalization.CultureInfo.InvariantCulture);
				EndTime1				= DateTime.Parse("03:30 PM", System.Globalization.CultureInfo.InvariantCulture);
				UseCloseTime1			= false;
				CloseTime1				= DateTime.Parse("03:50 PM", System.Globalization.CultureInfo.InvariantCulture);

				UseSession2				= false;
				StartTime2				= DateTime.Parse("09:30 AM", System.Globalization.CultureInfo.InvariantCulture);
				EndTime2				= DateTime.Parse("03:30 PM", System.Globalization.CultureInfo.InvariantCulture);
				UseCloseTime2			= false;
				CloseTime2				= DateTime.Parse("03:50 PM", System.Globalization.CultureInfo.InvariantCulture);

				UseSession3				= false;
				StartTime3				= DateTime.Parse("08:00 AM", System.Globalization.CultureInfo.InvariantCulture);
				EndTime3				= DateTime.Parse("09:30 AM", System.Globalization.CultureInfo.InvariantCulture);
				UseCloseTime3			= false;
				CloseTime3				= DateTime.Parse("03:50 PM", System.Globalization.CultureInfo.InvariantCulture);

				UseSession4				= false;
				StartTime4				= DateTime.Parse("10:00 AM", System.Globalization.CultureInfo.InvariantCulture);
				EndTime4				= DateTime.Parse("03:00 PM", System.Globalization.CultureInfo.InvariantCulture);
				UseCloseTime4			= false;
				CloseTime4				= DateTime.Parse("03:50 PM", System.Globalization.CultureInfo.InvariantCulture);

				UseSession5				= false;
				StartTime5				= DateTime.Parse("06:00 PM", System.Globalization.CultureInfo.InvariantCulture);
				EndTime5				= DateTime.Parse("08:00 AM", System.Globalization.CultureInfo.InvariantCulture);
				UseCloseTime5			= false;
				CloseTime5				= DateTime.Parse("03:50 PM", System.Globalization.CultureInfo.InvariantCulture);

				UseCloseTime			= false;
				CloseTime				= DateTime.Parse("03:50 PM", System.Globalization.CultureInfo.InvariantCulture);

				// Misc / Debug
				DisplayOCD				= true;
				StartingLine			= 2;
				PlotHist 				= true;
				ApplyDailyToHist		= true;
				StrategyName			= "Strike123Mngd";
				DrawLines				= true;
				DisableLogging			= false;
				UseOutput2				= true;
				UniqueID				= "#1";
				
			}
			else if (State == State.Configure)
			{
				chartInstrument = this.Instrument.FullName;
			}
			else if (State == State.DataLoaded)
			{
				// Set optimization mode var for indicator
				var optMode = OptimizationMode_123STRIKEAI.Simplified;
				if (OptimizationMode == OptMode.Standard)
					optMode = OptimizationMode_123STRIKEAI.Standard;
				else if (OptimizationMode == OptMode.Exhaustive)
					optMode = OptimizationMode_123STRIKEAI.Exhaustive;
				
				// Set reOptimization mode var for indicator
				var reOptMode = ReOptimizationMode_123STRIKEAI.WhenFlat;
				if (ReOptimizationMode == ReOptMode.IntervalInBars)
					reOptMode = ReOptimizationMode_123STRIKEAI.IntervalInBars;
				else if (ReOptimizationMode == ReOptMode.IntervalInBarsWhenFlat)
					reOptMode = ReOptimizationMode_123STRIKEAI.IntervalInBarsWhenFlat;
				
				// Hard-coded ind values:
				bool styleByNumber = false;
				int styleNumber = 0;
				bool showMaxSize = true;		// Position Size from ind?
				bool autoOptParams = true;		// Optimize On Load?
				bool showIET = false;
				int panelOpac = 50;
				int globalOpac = 100;
				bool colorPriceBars = false;
				int volatlP = 7;	// Volatility Period
				int volumeP = 34;	// Volume Period
				int fastMAP = 34;	// Fast MA Period
				int slowMAP = 34;	// Fast MA Period
				
				// Here, we want to enable trail -even if user has UseTrailingStop disabled- if UseRisk is enabled, because ind must have trailing stop 
				// OR we could just enable it all the time... Since we do not close trades onthe trail unliess enabled... (?)
				bool useTrail = UseRisk ? true : UseTrailingStop;
				
				strike123	= OneTwoThreeStrikeAI(Close, Convert.ToInt32(NumExitsL), Convert.ToInt32(NumExitsS), useTrail, UsePartialExits, 
												  TradingApproach_123STRIKEAI.LongAndShort, styleByNumber, styleNumber, showMaxSize, RiskAmount, 
												  OptimizeBars, ReOptimizeBars, ReOptAuto, reOptMode, autoOptParams, OptimizationTarget_123STRIKEAI.ProfitFactor, 
												  optMode, showIET, panelOpac, globalOpac, colorPriceBars, volatlP, volumeP, fastMAP, slowMAP, ExitOnVolume);

				// For Log to file
				if (UseOutput2)		PrintTo = PrintTo.OutputTab2;
				if (LogToFileName != "")
				{
					logPath = NinjaTrader.Core.Globals.UserDataDir + @"log\FiftyLevel\" + LogToFileName + "Log.txt";
					Print($"   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   logPath = {logPath}   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ   Θ");
					if (System.IO.File.Exists(logPath))
					{
						try
						{
							System.IO.File.Delete(logPath);
						}
						catch (Exception ex)
						{
							Print($"Could not delete log file (logPath): {ex.Message}");
						}
					}
				}
				else
					Print(@"   /  /  /  /  /  /  /  /  /  /  /   LogToFileName == null; No Log File will be made  \  \  \  \  \  \  \  \  \  \  \  \");

				tradeLong	= TradeLong;
				tradeShort	= TradeShort;

				Print($"                           StartTime1 begins as: {StartTime1.ToString()}");
				// Force StartTime1 to use today's date with the specified time to avoid template date persistence issues
				startTime1	= DateTime.Today.Add(StartTime1.TimeOfDay);
				startTime1 	= startTime1.AddHours(TimeZoneOffset);
				endTime1	= DateTime.Today.Add(EndTime1.TimeOfDay);
				endTime1	= endTime1.AddHours(TimeZoneOffset);
				closeTime1	= DateTime.Today.Add(CloseTime1.TimeOfDay);
				closeTime1	= closeTime1.AddHours(TimeZoneOffset);

				startTime2	= DateTime.Today.Add(startTime2.TimeOfDay);
				startTime2 	= startTime2.AddHours(TimeZoneOffset);
				endTime2	= DateTime.Today.Add(EndTime2.TimeOfDay);
				endTime2	= endTime2.AddHours(TimeZoneOffset);
				closeTime2	= DateTime.Today.Add(CloseTime2.TimeOfDay);
				closeTime2	= closeTime2.AddHours(TimeZoneOffset);

				startTime3	= DateTime.Today.Add(StartTime3.TimeOfDay);
				startTime3 	= startTime3.AddHours(TimeZoneOffset);
				endTime2	= DateTime.Today.Add(EndTime3.TimeOfDay);
				endTime3	= endTime3.AddHours(TimeZoneOffset);
				closeTime3	= DateTime.Today.Add(CloseTime3.TimeOfDay);
				closeTime3	= closeTime3.AddHours(TimeZoneOffset);

				startTime4	= DateTime.Today.Add(StartTime4.TimeOfDay);
				startTime4 	= startTime4.AddHours(TimeZoneOffset);
				endTime4	= DateTime.Today.Add(EndTime4.TimeOfDay);
				endTime4	= endTime4.AddHours(TimeZoneOffset);
				closeTime4	= DateTime.Today.Add(CloseTime4.TimeOfDay);
				closeTime4	= closeTime4.AddHours(TimeZoneOffset);

				startTime1	= DateTime.Today.Add(StartTime5.TimeOfDay);
				startTime5 	= startTime5.AddHours(TimeZoneOffset);
				endTime2	= DateTime.Today.Add(EndTime.TimeOfDay);
				endTime5	= endTime5.AddHours(TimeZoneOffset);
				closeTime1	= DateTime.Today.Add(CloseTime1.TimeOfDay);
				closeTime5	= closeTime5.AddHours(TimeZoneOffset);

				closeTime	= DateTime.Today.Add(CloseTime.TimeOfDay);
				closeTime	= closeTime.AddHours(TimeZoneOffset);
				
				ManageOCD(true);
			}

			else if (State == State.Historical)
			{
				if (ChartControl != null)
				{
					ChartControl.Dispatcher.InvokeAsync(() =>
					{
						CreateButtons();
					});
				}
				if (PlotHist)
					Print($"\n\n   «   «   «   «   «   «   HISTORICAL PROCESSING STARTED @ {DateTime.Now.ToString()}   »   »   »   »   »   »");
			}

			// Transition is called once as the object has finished processing historical data but before it starts to process realtime data.
			else if (State == State.Transition)
			{
				dailyProfit = 0;
				dailyLimitHit = false;
			}

			// Realtime is called once when the object begins to process realtime data.
			else if (State == State.Realtime)
			{
				if (PlotHist)
					Print($"\n\n   «   «   «   «   «   «   HISTORICAL PROCESSING ENDED @ {DateTime.Now.ToString()}   »   »   »   »   »   »");

				Print("\n\n\n\n\n-=-  -=-  SWITCHING TO REAL TIME PROCESSING  -=-  -=-  -=-  -=-  -=-  -=-  -=-  -=-  -=-  -=-  -=-  -=-  -=-  -=-  -=-");
				Print("");	Print("");	Print("");	Print("");
			}
		}

		protected override void OnBarUpdate()
		{
			if (BarsInProgress != 0) 
				return;

			if (CurrentBars[0] < 1)
				return;

			// Don't waste time on OCD for historical
			if (State == State.Historical  &&  !PlotHist)
				return;

			inSessionChanged = inSession;
			inSession = InTradeSession();
			inSessionChanged = (inSessionChanged != inSession);

			if (Bars.IsFirstBarOfSession)
			{
				// This is not useful for us because we do not let the EA run 24/5 such that this would trigger at 6pm ET
				// And more importantly, we do not do anthing with historical (yet); that is where this is important:
				// to reset some daily things that will be done with running over historical bars.

				if (Position.MarketPosition != MarketPosition.Flat  &&  FlattenEoS)
				{
					Log($"\n\n\n\nSESSION STARTED, FLATTEN ENABLED, AND POSITION NOT FLAT: CLOSING POSITION\n");
					Flatten();
				}
				dailyProfit = 0;	//SystemPerformance.AllTrades.TradesPerformance.NetProfit;
				dailyLimitHit = false;
				Log($"\nNEW SESSION (DAY) - RESET DAILY PROFIT - Time[0] = {Time[0].ToString()}\n");
			}

			// Close all trades at end of user-defined session
			if (IsCloseTime(activeSessionNum))
			{
				Log($"End of Session Close activated @ {Time[0].ToString()}; Closing all open orders");
				if (Position.MarketPosition != MarketPosition.Flat)
					Flatten();
				activeSessionNum = 0;
			}
			string log = "";

			// Do not look for trades until it has had enough bars to optimize
			if (CurrentBar > OptimizeBars  ||  State == State.Realtime)
			{
				double trailPriceL = Instrument.MasterInstrument.RoundToTickSize(strike123.LongTrailingStop[0]);
				double trailPriceS = Instrument.MasterInstrument.RoundToTickSize(strike123.ShortTrailingStop[0]);
				DrawSeenSignals();
				
				// If UseRisk is enabled, we always tell the ind to use trailing stop lines, because 
				// we have to in order to get the contract sizes based on initial SL (trail)
				int quantity = Convert.ToInt32(strike123.PositionSize[0]);
				int justEntered = 0;
				int WR_PF_Fail = 0;
				int partQty = 0;
				double dQtyPerX = 1.0;
				
				//if (strike123.LongEntry[0]  > 0)	Log($"             -----> CurrentBar = {CurrentBar}     tradeLong = {tradeLong}, strike123.LongEntry[0] = {strike123.LongEntry[0]}, strike123.LongIET[0] = {strike123.LongIET[0]} / {MinWinRateL}, strike123.ProfitFactor[0] = {strike123.ProfitFactor[0]} / {MinPF}, PositionSize = {quantity}");
				//if (strike123.ShortEntry[0] > 0)	Log($"             -----> CurrentBar = {CurrentBar}     tradeShort = {tradeShort}, strike123.ShortEntry[0] = {strike123.ShortEntry[0]}, strike123.ShortIET[0] = {strike123.ShortIET[0]} / {MinWinRateS}, strike123.ProfitFactor[0] = {strike123.ProfitFactor[0]} / {MinPF}, PositionSize = {quantity}");
			
				// Set 1 - Enter Long trade
				if (tradeLong  &&  strike123.LongEntry[0] > 0)
				{
					if (strike123.ProfitFactor[0] < MinPF)
						WR_PF_Fail = 1;
					if (WR_PF_Fail == 0  &&  strike123.LongIET[0] < MinWinRateL)
						WR_PF_Fail = 2;
					
					if (WR_PF_Fail > 0)
						DrawFailReason(WR_PF_Fail);
					else
					{
						if (!UseRisk)
							quantity = QuantityL;
						qtyLeft = quantity = Math.Min(quantity, MaxContracts);
						
						if (quantity > 0)
						{
							if (Position.MarketPosition == MarketPosition.Short)
								log += $"\n\nPOSITION is SHORT - REVERSAL!";
							else
								log += "\n";
							log += $"\nENTER LONG: Time = {Time[0].ToString()}";
							log += $"\n           Long Entry = {strike123.LongEntry[0]}";
							log += $"\n           Long IET = {strike123.LongIET[0]} / {MinWinRateL}";
							log += $"\n           Profit Factor = {strike123.ProfitFactor[0]} / {MinPF}";
							log += $"\n           Pos Size = {strike123.PositionSize[0]}";
							
							EnterLong(quantity, @"LongEntry");
							stopPrice = 0;
							justEntered = 1;
							exitsDone = 0;
							moveToBEDone = false;
							numExits = NumExitsL;
							partQty = PartXQtyL;
						}
						else
							Log($"ENTRY LONG: Time = {Time[0].ToString()} -- Aborted because Quantity from indicator is zero");
					}
				}
				
				// Set 2 - Enter Short trade
				if (tradeShort  &&  strike123.ShortEntry[0] > 0)
				{
					if (strike123.ProfitFactor[0] < MinPF)
						WR_PF_Fail = 3;
					if (WR_PF_Fail == 0  &&  strike123.ShortIET[0] < MinWinRateS)
						WR_PF_Fail = 4;
						
					if (WR_PF_Fail > 0)
						DrawFailReason(WR_PF_Fail);
					else
					{
						if (!UseRisk)
							quantity = QuantityS;
						qtyLeft = quantity = Math.Min(quantity, MaxContracts);
						
						if (quantity > 0)
						{
							if (Position.MarketPosition == MarketPosition.Long)
								log += $"\n\nPOSITION is LONG - REVERSAL!";
							else
								log += "\n";
							log += $"\nENTER SHORT: Time = {Time[0].ToString()}";
							log += $"\n           Short Entry = {strike123.ShortEntry[0]}";
							log += $"\n           Short IET = {strike123.ShortIET[0]} / {MinWinRateS}";
							log += $"\n           Profit Factor = {strike123.ProfitFactor[0]} / {MinPF}";
							log += $"\n           Pos Size = {strike123.PositionSize[0]}";
	
							EnterShort(quantity, @"ShortEntry");
							stopPrice = 0;
							justEntered = -1;
							exitsDone = 0;
							moveToBEDone = false;
							numExits = NumExitsS;
							partQty = PartXQtyS;
						}
						else
							Log($"ENTRY SHORT: Time = {Time[0].ToString()} -- Aborted because Quantity from indicator is zero");
					}
				}
				
				if (justEntered != 0)
				{
					// How we do rounding by Example:
					// starts with 10 lots, 4 exits, so 10/4 = 2.5, round to 3 for 1st exit.
					// now 7 lots remaining & 3 exits, so 7/3 = 2.333, round to 2 for 2nd exit
					// now 5 lots remaining & 2 exits, so 5/2 = 2.5, round to 3 for 2nd exit, leaves 2 open
					dQtyPerX = (double)quantity / (double)numExits;
					if (AutoExitQty)
					{
						exitQty = (int)Math.Max(1, Math.Round(dQtyPerX, MidpointRounding.AwayFromZero));
						Log($"CALCULATED QUANTITY TO BE: {quantity}. dQtyPerX = {dQtyPerX}, INITIAL EXIT QTY: {exitQty}, ROUND = {Math.Round(dQtyPerX, MidpointRounding.AwayFromZero)}");
					}
					else
						exitQty = partQty;

					// Put in an initial stop order (updated each bar)
					if (justEntered == 1  &&  trailPriceL > 0)
					{
						stopPrice = trailPriceL;
						ExitLongStopMarket(stopPrice);
						log += $"\nPLACED INITIAL SL on TRAIL VALUE: Time = {Time[0].ToString()}";
						log += $"\n           Sent ExitLongStopMarket({stopPrice})";
					}
					else if (justEntered == -1  &&  trailPriceS > 0)
					{
						stopPrice = trailPriceS;
						ExitShortStopMarket(stopPrice);
						log += $"\nPLACED INITIAL SL on TRAIL VALUE: Time = {Time[0].ToString()}";
						log += $"\n           Sent ExitShortStopMarket({stopPrice})";
					}
				}
				else if (Position.MarketPosition != MarketPosition.Flat)
				{
					bool exitedAll = false;
					int dir = (Position.MarketPosition == MarketPosition.Long) ? 1 : -1;
					
					// If already in trade, check for exits, Long first...
					if (dir == 1)
					{
						// Final Exit Long trade
						if (strike123.LongFinalExit[0] > 0)
						{
							log += $"\nFINAL EXIT LONG: Time = {Time[0].ToString()}";
							log += $"\n           Long Final Exit = {strike123.LongFinalExit[0]}";
							ExitLong();
							exitedAll = true;
						}
						
						// Partial Exit Long trade
						//Print($"     =================>>>      Long Partial Exit = {strike123.LongPartialExit[0]}");
						else if (strike123.LongPartialExit[0] > 0)
						{
							ExitLong(exitQty);
							numExits--;
							log += $"\nPARTIAL EXIT ({exitQty}) LONG: Time = {Time[0].ToString()}";
							log += $"\n           Long Partial Exit = {strike123.LongPartialExit[0]}, numExits now = {numExits}";

							if (numExits > 0)
							{
								qtyLeft -= exitQty;
								
								// Adjust exit quantity (needed for rounding)
								if (AutoExitQty)
								{
									if (numExits == 1)
										exitQty = qtyLeft;
									else
									{
										dQtyPerX = (double)qtyLeft / (double)numExits;
										exitQty = (int)Math.Max(1, Math.Round(dQtyPerX, MidpointRounding.AwayFromZero));
										log += $"\n           QTY LEFT: {qtyLeft};   CALCULATED NEW EXIT QTY: {exitQty}, ROUND({dQtyPerX}) = {Math.Round(dQtyPerX, MidpointRounding.AwayFromZero)}";
									}
								}
							}
						}
						
						else if (UseTrailingStop)
						{
							// Exit Long on trail
							if (CrossAbove(strike123.LongTrailingStop, Low, 1))
							{
								log += $"\nEXIT LONG on TRAIL: Time = {Time[0].ToString()}";
								log += $"\n           Low = {Low[0]}, Trailing Stop = {trailPriceL.ToString("F2")}";
								ExitLong();
								exitedAll = true;
							}
							// Update stop order
							else if (trailPriceL > 0)
							{
								Log($"trailPriceL ({trailPriceL}) > 0; current stopPrice = {stopPrice}");
								if (trailPriceL > stopPrice  ||  stopPrice == 0)
								{
									stopPrice = trailPriceL;
									ExitLongStopMarket(stopPrice);
									log += $"\n[RE]PLACED SL on TRAIL: Time = {Time[0].ToString()}";
									log += $"\n           Sent ExitLongStopMarket({stopPrice})";
								}
							}
						}
					}
					
					else if (dir == -1)
					{
						// Final Exit Short trade
						if (strike123.ShortFinalExit[0] > 0)
						{
							log += $"\nFINAL EXIT SHORT: Time = {Time[0].ToString()}";
							log += $"\n           Long Final Exit = {strike123.ShortFinalExit[0]}";
							ExitShort();
							exitedAll = true;
						}
						
						// Partial Exit Short trade
						else if (strike123.ShortPartialExit[0] > 0)
						{
							ExitShort(exitQty);
							numExits--;
							log += $"\nPARTIAL ({exitQty}) EXIT SHORT: Time = {Time[0].ToString()}";
							log += $"\n           Long Partial Exit = {strike123.ShortPartialExit[0]}, numExits now = {numExits}";

							if (numExits > 0)
							{
								qtyLeft -= exitQty;
								
								// Adjust exit quantity (needed for rounding)
								if (AutoExitQty)
								{
									if (numExits == 1)
										exitQty = qtyLeft;
									else
									{
										dQtyPerX = (double)qtyLeft / (double)numExits;
										exitQty = (int)Math.Max(1, Math.Round(dQtyPerX, MidpointRounding.AwayFromZero));
										log += $"\n           QTY LEFT: {qtyLeft};   CALCULATED NEW EXIT QTY: {exitQty}, ROUND({dQtyPerX}) = {Math.Round(dQtyPerX, MidpointRounding.AwayFromZero)}";
									}
								}
							}
						}
						
						else if (UseTrailingStop)
						{
							// Exit Short on trail
							if (CrossBelow(strike123.ShortTrailingStop, High, 1))
							{
								log += $"\nEXIT SHORT on TRAIL: Time = {Time[0].ToString()}";
								log += $"\n           High = {High[0]}, Trailing Stop = {trailPriceL.ToString("F2")}";
								ExitShort();
								exitedAll = true;
							}
							// Update stop order
							else if (trailPriceS > 0)
							{
								Log($"trailPriceS ({trailPriceS}) > 0; current stopPrice = {stopPrice}");
								if (trailPriceS < stopPrice  ||  stopPrice == 0)
								{
									stopPrice = trailPriceS;
									ExitShortStopMarket(stopPrice);
									log += $"\n[RE]PLACED SL on TRAIL: Time = {Time[0].ToString()}";
									log += $"\n           Sent ExitShortStopMarket({stopPrice})";
								}
							}
						}
					}			
	
					// Move SL to Breakeven?
					if (!exitedAll)
					{
						// For now we'll do MoveToBE on bar close, which means we have to just 
						// use the price the candle DID reach (High for But, Low for Sell)
						//double curPrice = Close[0];	// Use this if moved to tick series
						double curPrice = (dir == 1) ? High[0] : Low[0];
						double newSL;
						double entryPrice = AveEntryPrice();
						
						// Handle Move to Breakeven (e.g. when up 7 points, move SL to BE + 1 point)
						if (UseBreakeven  &&  !moveToBEDone  &&  BE_ActivTicks != 0)
						{
							if ((dir ==  1  &&  curPrice >= entryPrice + BE_ActivTicks*TickSize)
							||  (dir == -1  &&  curPrice <= entryPrice - BE_ActivTicks*TickSize))
							{
								newSL = entryPrice + dir*BE_Offset*TickSize;
								log += $"\nMOVE TO BREAKEVEN: dir = {dir}, Ave Price = {entryPrice}, Extreme Price = {curPrice}, set new SL to be {newSL}";
		
								if (newSL != 0  &&  (stopPrice == 0  ||  dir*newSL > dir*stopPrice))
								{
									stopPrice = newSL;
									if (dir == 1)	ExitLongStopMarket(newSL);
									else			ExitShortStopMarket(newSL);
									moveToBEDone = true;
								}
							}
						}
					}
				}
			}
			if (log != "")	Log(log);
			
			ManageOCD();
		}
		

		#region HELPER FUNCTIONS - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
		public void Flatten()
		{
			if (Position.MarketPosition == MarketPosition.Long)
				ExitLong();
			else if (Position.MarketPosition == MarketPosition.Short)
				ExitShort();
		}

		public double AveEntryPrice()
		{
			double avePrice = Position.AveragePrice;

			// If ave price does not fall on tick boundary, round up
			int numTicks = (int)(avePrice / TickSize);
			if (numTicks * TickSize != avePrice)
			{
				double prc = Instrument.MasterInstrument.RoundToTickSize(avePrice);
				avePrice = prc;
			}
			return avePrice;
		}

		public void DrawSeenSignals()
		{
			double L = Low[0] - 10 * TickSize;
			double H = High[0] + 10 * TickSize;
			
			if (strike123.LongEntry[0] > 0) 			DrawSymbol("LongEntry", false, 0, L, Brushes.Lime, "Diamond");
			else if (strike123.ShortEntry[0] > 0) 		DrawSymbol("ShortEntry", false, 0, H, Brushes.Red, "Diamond");
			
			else if (strike123.LongPartialExit[0] > 0) 	DrawSymbol("LongPartExit", false, 0, L, Brushes.Green, "TriUp");
			else if (strike123.ShortPartialExit[0] > 0) DrawSymbol("ShortPartExit", false, 0, H, Brushes.Firebrick, "TriDn");
			
			else if (strike123.LongFinalExit[0] > 0) 	DrawSymbol("LongFinalExit", false, 0, L, Brushes.Lime, "TriUp");
			else if (strike123.ShortFinalExit[0] > 0) 	DrawSymbol("ShortFinalExit", false, 0, H, Brushes.Red, "TriDn");
		}
		
		public void DrawSymbol(string tag, bool isAutoScale, int barsAgo, double y, Brush brush, string type="")
		{
			if (type == "TriUp")		Draw.TriangleUp(this, tag + "_" + CurrentBar, isAutoScale, barsAgo, y, brush);
			else if (type == "TriDn")	Draw.TriangleDown(this, tag + "_" + CurrentBar, isAutoScale, barsAgo, y, brush);
			else						Draw.Diamond(this, tag + "_" + CurrentBar, isAutoScale, barsAgo, y, brush);
		}

		public void DrawFailReason(int type)
		{
			double L = Low[0] - 10 * TickSize;
			double H = High[0] + 10 * TickSize;
			
			if (type == 1)		// Long Profit Factor failure
				Draw.Text(this, "LPF_" + CurrentBar, "LPF", 0, L, Brushes.Yellow);
			else if (type == 2)	// Long Win Rate failure
				Draw.Text(this, "LWR_" + CurrentBar, "LWR", 0, L, Brushes.Yellow);
			else if (type == 3)	// Short Profit Factor failure
				Draw.Text(this, "SPF_" + CurrentBar, "SPF", 0, H, Brushes.Yellow);
			else if (type == 4)	// Short Win Rate failure
				Draw.Text(this, "SWR_" + CurrentBar, "SWR", 0, H, Brushes.Yellow);
		}

		private string Bool2YN(bool b)
		{
			return (b) ? "Yes" : "No";
		}
		#endregion

		#region SESSION / FILTER  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
		/// <summary>
		/// Checks if all trade filters are satisfied for entering a trade
		/// </summary>
		/// <param name="dir">Direction of the trade ("Long" or "Short")</param>
		/// <returns>True if all filters pass, false otherwise</returns>
		public bool TradeFiltersOkay(string dir)
		{
			// Enter a trade if all enabled filters are satisfied
			bool passed = true;
			bool log = true;//(State == State.Realtime);
			string logStr = "";

			if (dir != "Long"  &&  dir != "Short")
			{
				logStr = $"Direction passed into TradeFiltersOkay ({dir}) must be 'Long' or 'Short'; Aborting";
				passed = false;
			}

			if (Position.Quantity != 0  ||  Position.MarketPosition != MarketPosition.Flat)
			{
				logStr = $"Cannot enter -already in position: MarketPosition = {Position.MarketPosition.ToString()}, Position.Quantity = {Position.Quantity}";
				passed = false;
			}

			if (!InTradeSession())
			{
				logStr = $"Out of session; Aborting {dir} entry";
				passed = false;
			}

			if (dir == "Long")
			{
				if (!tradeLong)
				{
					logStr = $"Long trade disabled; Aborting entry";
					passed = false;
				}
				else if (Position.MarketPosition == MarketPosition.Long)
				{
					logStr = $"Long trade already open; Aborting entry";
					passed = false;
				}
			}
			else if (dir == "Short")
			{
				if (!tradeShort)
				{
					logStr = $"Short trade disabled; Aborting entry";
					passed = false;
				}
				else if (Position.MarketPosition == MarketPosition.Short)
				{
					logStr = $"Short trade already open; Aborting entry";
					passed = false;
				}
			}

			if (log  &&  logStr != "") Log("TRADE FILTER FAILED: " + logStr);
			return passed;
		}

		/// <summary>
		/// Checks if trading is allowed based on current time and configured trading sessions
		/// </summary>
		/// <returns>True if trading is allowed, false otherwise</returns>
		public bool InTradeSession()
		{
			// Check trading sessions / days
			int activeSessionNum = 0;

			switch (Time[0].DayOfWeek)
			{
				case DayOfWeek.Sunday:		if (!TradeSunday)		return false;   else break;
				case DayOfWeek.Monday:		if (!TradeMonday)		return false;   else break;
				case DayOfWeek.Tuesday:		if (!TradeTuesday)		return false;   else break;
				case DayOfWeek.Wednesday:	if (!TradeWednesday)	return false;   else break;
				case DayOfWeek.Thursday:	if (!TradeThursday)		return false;   else break;
				case DayOfWeek.Friday:		if (!TradeFriday)		return false;   else break;
				default:															break;
			}

			if (!(UseSession1  ||  UseSession2  ||  UseSession3  ||  UseSession4  ||  UseSession5))
				return true;
			if (UseSession1)
				Log($"\n\n\n                    UseSession1 TRUE, startTime1 = {startTime1.ToString()}, endTime1 = {endTime1.ToString()}");
			if (UseSession1)
				if (CheckSession(1, startTime1, endTime1))
					return true;
			if (UseSession2)
				if (CheckSession(2, startTime2, endTime2))
					return true;
			if (UseSession3)
				if (CheckSession(3, startTime3, endTime3))
					return true;
			if (UseSession4)
				if (CheckSession(4, startTime4, endTime4))
					return true;
			if (UseSession5)
				if (CheckSession(5, startTime5, endTime5))
					return true;

			return false;
		}

		/// <summary>
		/// Checks if the current time is within a specific trading session
		/// </summary>
		/// <param name="num">Session number (for logging purposes)</param>
		/// <param name="startTime">Start time of the session</param>
		/// <param name="endTime">End time of the session</param>
		/// <returns>True if current time is within the session, false otherwise</returns>
		private bool CheckSession(int num, DateTime startTime, DateTime endTime)
		{
			// Check one trading session; return false if out-of-session
			bool okay = false;
			if (startTime.TimeOfDay < endTime.TimeOfDay)
			{
				if (Time[0].TimeOfDay >= startTime.TimeOfDay  &&  Time[0].TimeOfDay < endTime.TimeOfDay)
				{
					Log($"                     SESSION RETURNING OKAY!      Time[0] = {Time[0].ToString()}");
					okay = true;
				}
			}
			else if (startTime.TimeOfDay > endTime.TimeOfDay)
			{
				if (Time[0].TimeOfDay >= startTime.TimeOfDay  ||  Time[0].TimeOfDay < endTime.TimeOfDay)
					okay = true;
			}
			else // (startTime.TimeOfDay == endTime.TimeOfDay)
			{
				Log($"Start Time ({startTime.TimeOfDay.ToString()}) is the same as End Time ({endTime.TimeOfDay.ToString()}); Trading (always) approved");
				okay = true;
			}
			return okay;
		}

		/// <summary>
		/// Checks if it's time to close positions based on session close times
		/// </summary>
		/// <param name="activeSessionNum">The currently active trading session number (1-5)</param>
		/// <returns>True if positions should be closed, false otherwise</returns>
		public bool IsCloseTime(int activeSessionNum)
		{
			// Check if it's time to close positions based on session close times
			if (Position.MarketPosition == MarketPosition.Flat)
				return false;

			// If we are not using any trading session (trade all times), then just check the main close time
			if (!(UseSession1  ||  UseSession2  ||  UseSession3  ||  UseSession4  ||  UseSession5))
			{
				// Close all trades at end of user-defined session
				if (UseCloseTime  &&  Time[0].TimeOfDay >= closeTime.TimeOfDay)
					return true;
				return false;
			}

			// Otherwise, check which session is active
			// Close all trades at end of user-defined session
			if (activeSessionNum == 1  &&  UseCloseTime1  &&  Time[0].TimeOfDay >= closeTime1.TimeOfDay)
				return true;
			if (activeSessionNum == 2  &&  UseCloseTime2  &&  Time[0].TimeOfDay >= closeTime2.TimeOfDay)
				return true;
			if (activeSessionNum == 3  &&  UseCloseTime3  &&  Time[0].TimeOfDay >= closeTime3.TimeOfDay)
				return true;
			if (activeSessionNum == 4  &&  UseCloseTime4  &&  Time[0].TimeOfDay >= closeTime4.TimeOfDay)
				return true;
			if (activeSessionNum == 5  &&  UseCloseTime5  &&  Time[0].TimeOfDay >= closeTime5.TimeOfDay)
				return true;
			return false;
		}
		#endregion
		
		#region ON-CHART DISPLAY & LOG - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
		private void ManageOCD(bool shortVersion=false)
		{
			if (State == State.Historical)
				return;
			
			int StartingLine = 1;
			
			string tmp, text = "";
			for (int i=0; i < StartingLine; i++)
				text += "\n";

			text += " Strategy Name :\t\t" + StrategyName + "    v" + VERSION;
			if (UniqueID != "")
				text += "    ID: " + UniqueID;

			if (shortVersion)
				inSession = InTradeSession();
			text += "\n Long Trade Enabled :\t" + Bool2YN(tradeLong);
			text += "\n Short Trade Enabled :\t" + Bool2YN(tradeShort);
			text += "\n Trade Session Active :\t" + inSession + "\n";

			if (!shortVersion)
			{
//				text += "\n Last Trade Direction :\t" + lastTradeDir;
//				text += "\n Active Direction :\t\t" + Position.MarketPosition.ToString();
/*				if (DailyMinTarget != 0  ||  DailyMaxLoss != 0)
				{
					text += "\n";
					if (DailyMinTarget > 0)
						text += "\nDaily Min Profit :\t\t" + DailyMinTarget.ToString("C0");
					if (DailyMaxLoss > 0)
						text += "\nDaily Max Loss :\t\t" + DailyMaxLoss.ToString("C0");
					text += "\nDaily Profit :\t\t" + dailyProfit.ToString("C0");
					string pl = (dailyProfit > 0) ? "Profit" : "Loss";
					if (dailyLimitHit)
						text += "\nDaily " + pl + " Hit :\t\tYes - Trading Disabled";
					else
						text += "\nDaily Limit Hit :\t\tNo";
				}
*/
				text += "\n\n LONG PLOTS\t";
				text += "\n Entry :\t\t" + strike123.LongEntry[0];
				text += "\n IET :\t\t" + strike123.LongIET[0];
				text += "\n Partial Exit :\t" + strike123.LongPartialExit[0];
				text += "\n Final Exit :\t" + strike123.LongFinalExit[0];
				text += "\n Trail Price :\t" + strike123.LongTrailingStop[0].ToString("F2");
	
				text += "\n\n SHORT PLOTS\t";
				text += "\n Entry :\t\t" + strike123.ShortEntry[0];
				text += "\n IET :\t\t" + strike123.ShortIET[0];
				text += "\n Partial Exit :\t" + strike123.ShortPartialExit[0];
				text += "\n Final Exit :\t" + strike123.ShortFinalExit[0];
				text += "\n Trail Price :\t" + strike123.ShortTrailingStop[0].ToString("F2");
			}

			if (lastDashboard == text)	return;

			Draw.TextFixed(this, "OCD", text, NinjaTrader.NinjaScript.DrawingTools.TextPosition.TopLeft);
			lastDashboard = text;
		}

		private void Log(string message, [CallerMemberName] string memberName = "")
		{
			if (DisableLogging  ||  lastLogMsg == message)
				return;

			/// Debug time filtering...
			//if (Time[0] < dbg1  ||  Time[0] > dbg2)	return;

			/// Set this scripts Print() calls to the second output tab?
			if (UseOutput2)		PrintTo = PrintTo.OutputTab2;
			else				PrintTo = PrintTo.OutputTab1;

			string dateStr = "";
			string id = StrategyName;
			if (UniqueID != "")
				id += " " + UniqueID;

			if (lastLogMsg != message)
			{
				DateTime date = (State == State.Historical) ? Time[0] : DateTime.Now;
				dateStr = (State == State.Historical) ? date.ToString() : date.ToString("HH:mm:ss");

				string header = $"\n{id} - {chartInstrument} - [{memberName}]  -  {dateStr}  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -\n";

				/// Output just time if diff time but not new caller
				string output = (lastLogTime != dateStr) ? message + "   ( " + dateStr + " )" : message;

				/// If it is a new caller, write header line
				if (lastCaller != memberName  ||  lastLogTime != dateStr)
					LogToFile(header);

				LogToFile(output);
			}

			lastLogMsg = message;
			lastCaller = memberName;
			lastLogTime = dateStr;
		}

		private void LogToFile(string message)
		{
			/// Always print to the output window as well
			Print(message);

			/// Additionally logging to file is skipped if there is no file name
			if (logPath == "")
				return;
			//Print($"                                                                         logPath = {logPath}         LogToFileName = {LogToFileName}");

			/// Ensure the directory exists
			try
			{
				string folder = System.IO.Path.GetDirectoryName(logPath);
				if (!System.IO.Directory.Exists(folder))
					System.IO.Directory.CreateDirectory(folder);
			}
			catch (Exception ex)
			{
				Print($"LogToFile() error creating folder: {ex.Message}");
				return;
			}

			/// Append to the log file
			try
			{
				System.IO.File.AppendAllText(logPath, "\n" + message);
			}
			catch (Exception ex)
			{
				Print($"LogToFile() error writing file: {ex.Message}");
			}

		}
		#endregion

		#region BUTTONS - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
		private void OnMyButtonClick(object sender, RoutedEventArgs rea)
		{
			System.Windows.Controls.Button button = sender as System.Windows.Controls.Button;

			Log($"\n");
			bool lWasOn = tradeLong;
			bool sWasOn = tradeShort;
			if (button.Name == "longButton")
			{
				if (tradeLong)
				{
					button.Background = Brushes.Gray;
					button.Content = "Long Disabled";
					tradeLong = false;
					Log($"\nLONG ENTRIES DISABLED!");
				}
				else
				{
					button.Background = Brushes.Green;
					button.Content = "Long Enabled";
					tradeLong = true;
					Log($"\nLong entries Enabled!");
				}
			}
			else if (button.Name == "shortButton")
			{
				if (tradeShort)
				{
					button.Background = Brushes.Gray;
					button.Content = "Short Disabled";
					tradeShort = false;
					Log($"\nSHORT ENTRIES DISABLED!");
				}
				else
				{
					button.Background = Brushes.Crimson;
					button.Content = "Short Enabled";
					tradeShort = true;
					Log($"\nShort entries Enabled!");
				}
			}
			else if (button.Name == "exitButton")
			{
				if (Position.MarketPosition != MarketPosition.Flat)
				{
					Log($"\n\n=====================================================");
					Log($"User-Instigated Close of All Open Strategy Positions");
					Log($"=====================================================\n");
					Flatten();
				}
			}
			if (!tradeLong  &&  !tradeShort)
				if (lWasOn  ||  sWasOn)
					Log($"ALL ENTRIES DISABLED!");
			else if (tradeLong  &&  tradeShort)
				if (!lWasOn  ||  !sWasOn)
					Log($"ALL ENTRIES ENABLED!");
			Log($"\n");
		}

		protected void CreateButtons()
		{
			if (UserControlCollection.Contains(myGrid))
				return;

			myGrid = new System.Windows.Controls.Grid
			{
				Name = "MyCustomGrid",
				HorizontalAlignment = HorizontalAlignment.Left,
				VerticalAlignment = VerticalAlignment.Bottom,
			};

			myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
			myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
			myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
			myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
			myGrid.ColumnDefinitions.Add(new System.Windows.Controls.ColumnDefinition());
			myGrid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition());
			myGrid.RowDefinitions[0].Height	= new GridLength(20);
			myGrid.ColumnDefinitions[0].Width  = new GridLength(90);	// Long Enable
			myGrid.ColumnDefinitions[1].Width  = new GridLength(2);		// Blank spacer
			myGrid.ColumnDefinitions[2].Width  = new GridLength(90);	// Short Enable
			myGrid.ColumnDefinitions[3].Width  = new GridLength(6);		// Blank spacer
			myGrid.ColumnDefinitions[4].Width  = new GridLength(60);	// Exit All

			longButton = new System.Windows.Controls.Button
			{
				Name = "longButton",
				Content = "Long Enabled",
				Foreground = Brushes.White,
				Background = Brushes.Green,
				Height = 25,
				Width = 90,
				FontSize = 12,
				HorizontalAlignment = HorizontalAlignment.Center,
				VerticalAlignment = VerticalAlignment.Center,
				Focusable = false
			};
			// Set initial state based on user input
			if (!tradeLong)
			{
				longButton.Content = "Long Disabled";
				longButton.Background = Brushes.Gray; // Change appearance to "pressed"
			}

			shortButton = new System.Windows.Controls.Button
			{
				Name = "shortButton",
				Content = "Short Enabled",
				Foreground = Brushes.White,
				Background = Brushes.Crimson,
				Height = 25,
				Width = 90,
				FontSize = 12,
				HorizontalAlignment = HorizontalAlignment.Center,
				VerticalAlignment = VerticalAlignment.Center,
				Focusable = false
			};
			// Set initial state based on user input
			if (!tradeShort)
			{
				shortButton.Content = "Short Disabled";
				shortButton.Background = Brushes.Gray; // Change appearance to "pressed"
			}

			exitButton = new System.Windows.Controls.Button
			{
				Name = "exitButton",
				Content = "Exit All",
				Foreground = Brushes.White,
				Background = Brushes.DarkMagenta,
				Height = 25,
				Width = 60,
				FontSize = 12,
				HorizontalAlignment = HorizontalAlignment.Center,
				VerticalAlignment = VerticalAlignment.Center,
				Focusable = false
			};

			longButton.Click += OnMyButtonClick;
			shortButton.Click += OnMyButtonClick;
			exitButton.Click += OnMyButtonClick;

			System.Windows.Controls.Grid.SetColumn(longButton, 0);
			System.Windows.Controls.Grid.SetRow(longButton, 0);
			System.Windows.Controls.Grid.SetColumn(shortButton, 2);
			System.Windows.Controls.Grid.SetRow(shortButton, 0);
			System.Windows.Controls.Grid.SetColumn(exitButton, 4);
			System.Windows.Controls.Grid.SetRow(exitButton, 0);

			myGrid.Children.Add(longButton);
			myGrid.Children.Add(shortButton);
			myGrid.Children.Add(exitButton);

			UserControlCollection.Add(myGrid);
		}

		public void DisposeButtons()
		{
			if (myGrid != null)
			{
				if (longButton != null)
				{
					myGrid.Children.Remove(longButton);
					longButton.Click -= OnMyButtonClick;
					longButton = null;
				}
				if (shortButton != null)
				{
					myGrid.Children.Remove(shortButton);
					shortButton.Click -= OnMyButtonClick;
					shortButton = null;
				}
				if (exitButton != null)
				{
					myGrid.Children.Remove(exitButton);
					exitButton.Click -= OnMyButtonClick;
					exitButton = null;
				}
			}
		}

		private void UpdateTradeButtons(bool longOn, bool shortOn)
		{
			string stateL = (longOn)  ? " Enabled" : " Disabled";
			string stateS = (shortOn) ? " Enabled" : " Disabled";
			Brush bgLong  = (longOn)  ? Brushes.Green : Brushes.Gray;
			Brush bgShort = (shortOn) ? Brushes.Crimson : Brushes.Gray;

			if (Dispatcher.CheckAccess())
			{
				longButton.Content = "Long" + stateL;
				longButton.Background = bgLong;
				shortButton.Content = "Short" + stateS;
				shortButton.Background = bgShort;
			}
			else
			{
				Dispatcher.Invoke(() => {
					longButton.Content = "Long" + stateL;
					longButton.Background = bgLong;
				});
				Dispatcher.Invoke(() => {
					shortButton.Content = "Short" + stateS;
					shortButton.Background = bgShort;
				});
			}
		}

		#endregion

		#region PROPERTIES - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
		#region TimeZone    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Range(-23, 23)]
		[Display(Name = "Timezone Offset", Description = "Number of hours to add the session times", Order=0, GroupName = "00. TimeZone")]
		public int TimeZoneOffset
		{ get; set; }
		#endregion

		#region Entry  ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Display(Name="Trade Long", Description="Trade Short signals from the indicator", Order=0, GroupName="01. Entry")]
		public bool TradeLong
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="Trade Short", Description="Trade Long signals from the indicator", Order=2, GroupName="01. Entry")]
		public bool TradeShort
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="Use Risk to Calc Lots", Description="Allow indicator to calculate the number of contracts to trade based on dollar risk amount and initial SL; Trailing Stop must be Enabled", Order=5, GroupName="01. Entry")]
		public bool UseRisk
		{ get; set; }

		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="Risk Amount", Description="Amount to risk per trade", Order=7, GroupName="01. Entry")]
		public int RiskAmount
		{ get; set; }

		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="Quantity Long", Description="Amount of contracts to enter on a long signal", Order=10, GroupName="01. Entry")]
		public int QuantityL
		{ get; set; }

		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="Quantity Short", Description="Amount of contracts to enter on a short signal", Order=12, GroupName="01. Entry")]
		public int QuantityS
		{ get; set; }

		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="Maximum Contracts", Description="The maximum number of contracts the Strategy will place at once", Order=13, GroupName="01. Entry")]
		public int MaxContracts
		{ get; set; }

		[NinjaScriptProperty]
		[Range(0, 999)]
		[Display(Name="Min Profit Factor", Description="Minimum Profit Factor to take a trade", Order=15, GroupName="01. Entry")]
		public double MinPF
		{ get; set; }

		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name="Long Min Win Rate", Description="Minimum win rate percentage to take a long trade", Order=17, GroupName="01. Entry")]
		public int MinWinRateL
		{ get; set; }

		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name="Short Min Win Rate", Description="Minimum win rate percentage to take a short trade", Order=19, GroupName="01. Entry")]
		public int MinWinRateS
		{ get; set; }
		#endregion

		#region Move to BreakEven     ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Display(Name = "Use Break Even", Description = "When first TP is triggerd, move remaining stop to Break Even", Order=0, GroupName = "03. Break Even")]
		public bool UseBreakeven
		{ get; set; }

		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name = "Activation Ticks", Description = "Ticks in profit to activate Breakeven", Order=1, GroupName = "03. Break Even")]
		public int BE_ActivTicks
		{ get; set; }

		[NinjaScriptProperty]
		[Range(int.MinValue, int.MaxValue)]
		[Display(Name="BreakEven Offset", Description = "Win Break Even offset from entry price", Order=3, GroupName = "03. Break Even")]
		public int BE_Offset
		{ get; set; }
		#endregion

		#region Exit   ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Display(Name="Flatten on New Day", Description="Exit all trades at the start of each new daily session (likely 12am EST)", Order=0, GroupName="02. Exit")]
		public bool FlattenEoS
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="Exit on Volume Reversal", Description="Partial Exit on High Volume Reversal", Order=1, GroupName="02. Exit")]
		public bool ExitOnVolume
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="Use Trailing Stop", Description="Use Trailing Stop line from indicator", Order=2, GroupName="02. Exit")]
		public bool UseTrailingStop
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="Use Partial Exits", Description="Use Partial Exits established by indicator", Order=3, GroupName="02. Exit")]
		public bool UsePartialExits
		{ get; set; }

		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="Number of Long Exits", Description="Number of Long [partial] Exits", Order=5, GroupName="02. Exit")]
		public int NumExitsL
		{ get; set; }

		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="Number of Short Exits", Description="Number of Short [partial] Exits", Order=7, GroupName="02. Exit")]
		public int NumExitsS
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="Auto Exit Quantity", Description="Calculate the lots to exit; e.g. if Exits = 4, then each removes 1/4 of the trade", Order=13, GroupName="02. Exit")]
		public bool AutoExitQty
		{ get; set; }

		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="Long Partial Exit Qty", Description="Amount of contracts to close on each partial exit signal", Order=15, GroupName="02. Exit")]
		public int PartXQtyL
		{ get; set; }

		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="Short Partial Exit Qty", Description="Amount of contracts to close on each partial exit signal", Order=17, GroupName="02. Exit")]
		public int PartXQtyS
		{ get; set; }
		#endregion

		#region Daily Targets    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Daily Target Min $", Description = "Profit amount at which to stop trading for the day", Order=0, GroupName = "04. Daily Targets")]
		public int DailyMinTarget
		{ get; set; }

		[NinjaScriptProperty]
		[Range(0, int.MaxValue)]
		[Display(Name = "Daily Loss Max $", Description = "Loss amount at which to stop trading for the day", Order=3, GroupName = "04. Daily Targets")]
		public int DailyMaxLoss
		{ get; set; }
		#endregion

		#region Indicator   ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Range(100, int.MaxValue)]
		[Display(Name="Optimization Bars", Description="Number of bars to use for Optimization", Order=0, GroupName="03. Indicator")]
		public int OptimizeBars
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name="Optimization Mode", Description="Optimization Mode for indicator", Order=3, GroupName="03. Indicator")]
		public OptMode OptimizationMode
		{ get; set; }
		
		[NinjaScriptProperty]
		[Display(Name="ReOptimize Automatically", Description="Use the indicator's Re-Optimize Automatcally", Order=6, GroupName="03. Indicator")]
		public bool ReOptAuto
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="ReOptimization Mode", Description="When to ReOptimize", Order=7, GroupName="03. Indicator")]
		public ReOptMode ReOptimizationMode
		{ get; set; }

		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="ReOptimization Bars", Description="How often (in bars) it will ReOptimize (based on ReOptimization Mode)", Order=8, GroupName="03. Indicator")]
		public int ReOptimizeBars
		{ get; set; }
		#endregion


		#region Trading Days     ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Display(Name = "Trade Sunday", Description = "Toggle Sunday Trading", Order=0, GroupName = "05. Trading Days")]
		public bool TradeSunday
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Trade Monday", Description = "Toggle Monday Trading", Order=1, GroupName = "05. Trading Days")]
		public bool TradeMonday
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Trade Tuesday", Description = "Toggle Tuesday Trading", Order=2, GroupName = "05. Trading Days")]
		public bool TradeTuesday
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Trade Wednesday", Description = "Toggle Wednesday Trading", Order=3, GroupName = "05. Trading Days")]
		public bool TradeWednesday
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Trade Thursday", Description = "Toggle Thursday Trading", Order=4, GroupName = "05. Trading Days")]
		public bool TradeThursday
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Trade Friday", Description = "Toggle Friday Trading", Order=5, GroupName = "05. Trading Days")]
		public bool TradeFriday
		{ get; set; }
		#endregion
		#region Session 1 Hours  ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Display(Name = "Use Session 1", Description = "Use Trading Session #1", Order=0, GroupName = "06a. Session 1 Hours")]
		public bool UseSession1
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "Start Time", Description = "Enter Start Time for Session 1", Order=4, GroupName = "06a. Session 1 Hours")]
		public DateTime StartTime1
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "End Time", Description = "Enter End Time for Session 1", Order=5, GroupName = "06a. Session 1 Hours")]
		public DateTime EndTime1
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Use Close Time", Description = "Enable to close all Session 1 trades at given time", Order=7, GroupName = "06a. Session 1 Hours")]
		public bool UseCloseTime1
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "Close Time", Description = "Enter Time to Close Session 1 Trades", Order=9, GroupName = "06a. Session 1 Hours")]
		public DateTime CloseTime1
		{ get; set; }
		#endregion
		#region Session 2 Hours  ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Display(Name = "Use Session 2", Description = "Use Trading Session #2", Order=0, GroupName = "06b. Session 2 Hours")]
		public bool UseSession2
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "Start Time", Description = "Enter Start Time for Session 2", Order=4, GroupName = "06b. Session 2 Hours")]
		public DateTime StartTime2
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "End Time", Description = "Enter End Time for Session 2", Order=5, GroupName = "06b. Session 2 Hours")]
		public DateTime EndTime2
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Use Close Time", Description = "Enable to close all Session 2 trades at given time", Order=7, GroupName = "06b. Session 2 Hours")]
		public bool UseCloseTime2
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "Close Time", Description = "Enter Time to Close Session 2 Trades", Order=9, GroupName = "06b. Session 2 Hours")]
		public DateTime CloseTime2
		{ get; set; }
		#endregion
		#region Session 3 Hours  ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Display(Name = "Use Session 3", Description = "Use Trading Session #3", Order=0, GroupName = "06c. Session 3 Hours")]
		public bool UseSession3
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "Start Time", Description = "Enter Start Time for Session 3", Order=4, GroupName = "06c. Session 3 Hours")]
		public DateTime StartTime3
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "End Time", Description = "Enter End Time for Session 3", Order=5, GroupName = "06c. Session 3 Hours")]
		public DateTime EndTime3
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Use Close Time", Description = "Enable to close all Session 3 trades at given time", Order=7, GroupName = "06c. Session 3 Hours")]
		public bool UseCloseTime3
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "Close Time", Description = "Enter Time to Close Session 3 Trades", Order=9, GroupName = "06c. Session 3 Hours")]
		public DateTime CloseTime3
		{ get; set; }
		#endregion
		#region Session 4 Hours  ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Display(Name = "Use Session 4", Description = "Use Trading Session #4", Order=0, GroupName = "06d. Session 4 Hours")]
		public bool UseSession4
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "Start Time", Description = "Enter Start Time for Session 4", Order=4, GroupName = "06d. Session 4 Hours")]
		public DateTime StartTime4
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "End Time", Description = "Enter End Time for Session 4", Order=5, GroupName = "06d. Session 4 Hours")]
		public DateTime EndTime4
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Use Close Time", Description = "Enable to close all Session 4 trades at given time", Order=7, GroupName = "06d. Session 4 Hours")]
		public bool UseCloseTime4
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "Close Time", Description = "Enter Time to Close Session 4 Trades", Order=9, GroupName = "06d. Session 4 Hours")]
		public DateTime CloseTime4
		{ get; set; }
		#endregion
		#region Session 5 Hours  ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Display(Name = "Use Session 5", Description = "Use Trading Session #5", Order=0, GroupName = "06e. Session 5 Hours")]
		public bool UseSession5
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "Start Time", Description = "Enter Start Time for Session 5", Order=4, GroupName = "06e. Session 5 Hours")]
		public DateTime StartTime5
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "End Time", Description = "Enter End Time for Session 5", Order=5, GroupName = "06e. Session 5 Hours")]
		public DateTime EndTime5
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Use Close Time", Description = "Enable to close all Session 5 trades at given time", Order=7, GroupName = "06e. Session 5 Hours")]
		public bool UseCloseTime5
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "Close Time", Description = "Enter Time to Close Session 5 Trades", Order=9, GroupName = "06e. Session 5 Hours")]
		public DateTime CloseTime5
		{ get; set; }
		#endregion
		#region No-Session Close ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Display(Name = "Use Close Time", Description = "Used only if sessions are disabled", Order=0, GroupName = "06f. No-Session Close")]
		public bool UseCloseTime
		{ get; set; }

		[NinjaScriptProperty]
		[PropertyEditor("NinjaTrader.Gui.Tools.TimeEditorKey")]
		[Display(Name = "Close Time", Description = "Enter Time to Close All Open Trades", Order=3, GroupName = "06f. No-Session Close")]
		public DateTime CloseTime
		{ get; set; }
		#endregion

		#region Misc / Debug     ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙    ∙
		[NinjaScriptProperty]
		[Display(Name = "Display OCD", Description = "Show the On-Chart Display", Order=0, GroupName = "07. Misc / Debug")]
		public bool DisplayOCD
		{ get; set; }

		[NinjaScriptProperty]
		[Range(0, 50)]
		[Display(Name = "Starting Line", Description = "How many lines down to space the OCD", Order=3, GroupName = "07. Misc / Debug")]
		public int StartingLine
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Plot Historical", Description = "Plot historical trades from DB", Order=6, GroupName = "07. Misc / Debug")]
		public bool PlotHist
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Daily Targets on Historical", Description = "Apply Daily Profit / Loss settings to historical trades?", Order=9, GroupName = "07. Misc / Debug")]
		public bool ApplyDailyToHist
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Strategy Name", Description = "Name of Strategy", Order=12, GroupName = "07. Misc / Debug")]
		public string StrategyName
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Draw Horz Lines", Description = "Draw the 50, 100, and 26 lines", Order=24, GroupName = "07. Misc / Debug")]
		public bool DrawLines
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Disable Logging", Description = "Disable logging to Ninjascript Output windows", Order=27, GroupName = "07. Misc / Debug")]
		public bool DisableLogging
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Use Output 2", Description = "Send logging to second Output window", Order=30, GroupName = "07. Misc / Debug")]
		public bool UseOutput2
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Unique ID (v" + VERSION + ")", Description = "Unique number to identify strategy in Output window", Order=33, GroupName = "07. Misc / Debug")]
		public string UniqueID
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name = "Log to File", Description = "Log to file as well as NinjaScript Output window (name of log file -leave blank for same name as strategy", Order=36, GroupName = "07. Misc / Debug")]
		public string LogToFileName
		{ get; set; }
		#endregion
		
		#endregion
	}
}
