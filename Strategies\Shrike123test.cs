#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

//This namespace holds Strategies in this folder and is required. Do not change it. 
namespace NinjaTrader.NinjaScript.Strategies
{
	public class Shrike123test : Strategy
	{
		private NinjaTrader.NinjaScript.Indicators.TradingIndicators.OneTwoThreeStrikeAI OneTwoThreeStrikeAI1;
		private NinjaTrader.NinjaScript.Indicators.TradingIndicators.OneTwoThreeStrikeAI OneTwoThreeStrikeAI2;
		private NinjaTrader.NinjaScript.Indicators.TradingIndicators.OneTwoThreeStrikeAI OneTwoThreeStrikeAI3;

		protected override void OnStateChange()
		{
			if (State == State.SetDefaults)
			{
				Description									= @"Test EA to see how to code this ind.";
				Name										= "Shrike123test";
				Calculate									= Calculate.OnBarClose;
				EntriesPerDirection							= 1;
				EntryHandling								= EntryHandling.AllEntries;
				IsExitOnSessionCloseStrategy				= false;
				ExitOnSessionCloseSeconds					= 30;
				IsFillLimitOnTouch							= false;
				MaximumBarsLookBack							= MaximumBarsLookBack.Infinite;
				OrderFillResolution							= OrderFillResolution.Standard;
				Slippage									= 0;
				StartBehavior								= StartBehavior.WaitUntilFlat;
				TimeInForce									= TimeInForce.Gtc;
				TraceOrders									= false;
				RealtimeErrorHandling						= RealtimeErrorHandling.StopCancelClose;
				StopTargetHandling							= StopTargetHandling.PerEntryExecution;
				BarsRequiredToTrade							= 20;
				// Disable this property for performance gains in Strategy Analyzer optimizations
				// See the Help Guide for additional information
				IsInstantiatedOnEachOptimizationIteration	= true;
				RiskAmount					= 500;
				NumExitsL					= 4;
				NumExitsS					= 4;
			}
			else if (State == State.Configure)
			{
			}
			else if (State == State.DataLoaded)
			{				
				OneTwoThreeStrikeAI1				= OneTwoThreeStrikeAI(Close, Convert.ToInt32(NumExitsL), Convert.ToInt32(NumExitsS), true, true, TradingApproach_123STRIKEAI.LongAndShort, false, 0, true, RiskAmount, 1000, 30, true, ReOptimizationMode_123STRIKEAI.WhenFlat, true, OptimizationTarget_123STRIKEAI.ProfitFactor, OptimizationMode_123STRIKEAI.Standard, false, 50, 100, false, 7, 34, 7, 21, false);
				OneTwoThreeStrikeAI2				= OneTwoThreeStrikeAI(Close, Convert.ToInt32(NumExitsL), Convert.ToInt32(NumExitsS), true, true, TradingApproach_123STRIKEAI.LongAndShort, false, 0, true, RiskAmount, 1000, 30, true, ReOptimizationMode_123STRIKEAI.WhenFlat, true, OptimizationTarget_123STRIKEAI.ProfitFactor, OptimizationMode_123STRIKEAI.Standard, true, 50, 100, false, 7, 34, 7, 21, false);
				OneTwoThreeStrikeAI3				= OneTwoThreeStrikeAI(Close, Convert.ToInt32(NumExitsL), Convert.ToInt32(NumExitsS), true, true, TradingApproach_123STRIKEAI.LongAndShort, false, 0, true, RiskAmount, 1000, 30, false, ReOptimizationMode_123STRIKEAI.WhenFlat, true, OptimizationTarget_123STRIKEAI.ProfitFactor, OptimizationMode_123STRIKEAI.Standard, true, 50, 100, false, 7, 34, 7, 21, false);
			}
		}

		protected override void OnBarUpdate()
		{
			if (BarsInProgress != 0) 
				return;

			if (CurrentBars[0] < 1)
				return;

			 // Set 1
			if ((OneTwoThreeStrikeAI1.LongEntry[0] > 0)
				 && (OneTwoThreeStrikeAI2.LongIET[0] > 60))
			{
				EnterLong(Convert.ToInt32(OneTwoThreeStrikeAI2.PositionSize[0]), @"LongEntry");
			}
			
			 // Set 2
			if ((OneTwoThreeStrikeAI1.ShortEntry[0] > 0)
				 && (OneTwoThreeStrikeAI2.ShortIET[0] > 60))
			{
				EnterShort(Convert.ToInt32(OneTwoThreeStrikeAI2.PositionSize[0]), @"ShortEntry");
			}
			
			 // Set 3
			if ((CrossAbove(OneTwoThreeStrikeAI1.LongTrailingStop, Low, 1))
				 && (Position.MarketPosition == MarketPosition.Long))
			{
				ExitLong(Convert.ToInt32(OneTwoThreeStrikeAI3.PositionSize[0]), @"ExitLong", @"EnterLong");
			}
			
			 // Set 4
			if ((CrossBelow(OneTwoThreeStrikeAI1.ShortTrailingStop, High, 1))
				 && (Position.MarketPosition == MarketPosition.Short))
			{
				ExitShort(Convert.ToInt32(OneTwoThreeStrikeAI2.ShortTrailingStop[0]), @"ExitShort", @"EnterShort");
			}
			
			 // Set 5
			if ((OneTwoThreeStrikeAI1.LongPartialExit[0] > 0)
				 && (Position.MarketPosition == MarketPosition.Long))
			{
				ExitLong(1, @"PartExitL", @"EnterLong");
			}
			
			 // Set 6
			if ((OneTwoThreeStrikeAI1.ShortPartialExit[0] > 0)
				 && (Position.MarketPosition == MarketPosition.Short))
			{
				ExitShort(1, @"PartExitS", @"EnterShort");
			}
			
			 // Set 7
			if ((OneTwoThreeStrikeAI1.LongFinalExit[0] > 0)
				 && (Position.MarketPosition == MarketPosition.Long))
			{
				ExitLong(Convert.ToInt32(Position.Quantity), @"PartExitL", @"EnterLong");
			}
			
			 // Set 8
			if ((OneTwoThreeStrikeAI1.ShortFinalExit[0] > 0)
				 && (Position.MarketPosition == MarketPosition.Short))
			{
				ExitShort(Convert.ToInt32(Position.Quantity), @"PartExitS", @"EnterShort");
			}
			
		}

		#region Properties
		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="RiskAmount", Description="Amount to risk per trade", Order=1, GroupName="Parameters")]
		public int RiskAmount
		{ get; set; }

		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="NumExitsL", Description="Numer of Long [partial] Exits", Order=2, GroupName="Parameters")]
		public int NumExitsL
		{ get; set; }

		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="NumExitsS", Description="Number of Short [partial] Exits", Order=3, GroupName="Parameters")]
		public int NumExitsS
		{ get; set; }
		#endregion

	}
}
