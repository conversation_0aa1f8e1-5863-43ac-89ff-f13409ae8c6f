﻿<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <AssemblyName>NinjaTrader.Custom</AssemblyName>
    <DefineConstants>$(DefineConstants);$(CiConstants)</DefineConstants>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <ImportWindowsDesktopTargets>true</ImportWindowsDesktopTargets>
    <LangVersion>9.0</LangVersion>
    <OutputPath>bin\$(Configuration)\</OutputPath>
    <OutputType>Library</OutputType>
    <Platforms>x64</Platforms>
    <TargetFramework>net48</TargetFramework>
    <UseWPF>true</UseWPF>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <CodeAnalysisRuleSet>ManagedMinimumRules.ruleset</CodeAnalysisRuleSet>
    <DocumentationFile>bin\Debug\NinjaTrader.Custom.XML</DocumentationFile>
    <GenerateSerializationAssemblies>Off</GenerateSerializationAssemblies>
    <NoWarn>1591</NoWarn>
    <PlatformTarget>x64</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <CodeAnalysisRuleSet>ManagedMinimumRules.ruleset</CodeAnalysisRuleSet>
    <DebugSymbols>true</DebugSymbols>
    <DocumentationFile>bin\Release\NinjaTrader.Custom.XML</DocumentationFile>
    <GenerateSerializationAssemblies>Off</GenerateSerializationAssemblies>
    <NoWarn>1591</NoWarn>
    <PlatformTarget>x64</PlatformTarget>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="mscorlib">
      <HintPath>C:\WINDOWS\Microsoft.NET\Framework64\v4.0.30319\mscorlib.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xaml" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="InfragisticsWPF4.DataPresenter.v15.1">
      <HintPath>C:\Program Files\NinjaTrader 8\bin\InfragisticsWPF4.DataPresenter.v15.1.dll</HintPath>
    </Reference>
    <Reference Include="Infralution.Localization.Wpf">
      <HintPath>C:\Program Files\NinjaTrader 8\bin\Infralution.Localization.Wpf.dll</HintPath>
    </Reference>
    <Reference Include="NinjaTrader.Core">
      <HintPath>C:\Program Files\NinjaTrader 8\bin\NinjaTrader.Core.dll</HintPath>
    </Reference>
    <Reference Include="NinjaTrader.Gui">
      <HintPath>C:\Program Files\NinjaTrader 8\bin\NinjaTrader.Gui.dll</HintPath>
    </Reference>
    <Reference Include="SharpDX">
      <HintPath>C:\Program Files\NinjaTrader 8\bin\SharpDX.dll</HintPath>
    </Reference>
    <Reference Include="SharpDX.Direct2D1">
      <HintPath>C:\Program Files\NinjaTrader 8\bin\SharpDX.Direct2D1.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>S:\Documents\NinjaTrader 8\bin\Custom\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>C:\WINDOWS\Microsoft.NET\Framework\v4.0.30319\System.Data.Common.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <HintPath>C:\WINDOWS\Microsoft.NET\Framework\v4.0.30319\System.Data.DataSetExtensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>C:\WINDOWS\Microsoft.NET\Framework\v4.0.30319\System.Data.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.Entity.Design">
      <HintPath>C:\WINDOWS\Microsoft.NET\Framework\v4.0.30319\System.Data.Entity.Design.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.Entity">
      <HintPath>C:\WINDOWS\Microsoft.NET\Framework\v4.0.30319\System.Data.Entity.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.Linq">
      <HintPath>C:\WINDOWS\Microsoft.NET\Framework\v4.0.30319\System.Data.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.OracleClient">
      <HintPath>C:\WINDOWS\Microsoft.NET\Framework\v4.0.30319\System.Data.OracleClient.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.Services.Client">
      <HintPath>C:\WINDOWS\Microsoft.NET\Framework\v4.0.30319\System.Data.Services.Client.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.Services.Design">
      <HintPath>C:\WINDOWS\Microsoft.NET\Framework\v4.0.30319\System.Data.Services.Design.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.Services">
      <HintPath>C:\WINDOWS\Microsoft.NET\Framework\v4.0.30319\System.Data.Services.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.SqlXml">
      <HintPath>C:\WINDOWS\Microsoft.NET\Framework\v4.0.30319\System.Data.SqlXml.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows.Forms.DataVisualization.Design">
      <HintPath>C:\WINDOWS\Microsoft.NET\Framework\v4.0.30319\System.Windows.Forms.DataVisualization.Design.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows.Forms.DataVisualization">
      <HintPath>C:\WINDOWS\Microsoft.NET\Framework\v4.0.30319\System.Windows.Forms.DataVisualization.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows.Forms">
      <HintPath>C:\WINDOWS\Microsoft.NET\Framework\v4.0.30319\System.Windows.Forms.dll</HintPath>
    </Reference>
    <Reference Include="NetMQ">
      <HintPath>S:\Documents\NinjaTrader 8\bin\Custom\NetMQ.dll</HintPath>
    </Reference>
    <Reference Include="Npgsql">
      <HintPath>S:\Documents\NinjaTrader 8\bin\Custom\Npgsql.dll</HintPath>
    </Reference>
    <Reference Include="AsyncIO">
      <HintPath>S:\Documents\NinjaTrader 8\bin\Custom\AsyncIO.dll</HintPath>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>C:\Windows\Microsoft.NET\Framework64\v4.0.30319\mscorlib.dll</HintPath>
    </Reference>
    <Reference Include="PowerBundle360AIv1.03">
      <HintPath>S:\Documents\NinjaTrader 8\bin\Custom\PowerBundle360AIv1.03.dll</HintPath>
    </Reference>
    <Reference Include="NinjaTrader.Vendor">
      <HintPath>S:\Documents\NinjaTrader 8\bin\Custom\NinjaTrader.Vendor.dll</HintPath>
    </Reference>
    <Reference Include="WindowsBase">
      <HintPath>C:\Windows\Microsoft.NET\Framework\v4.0.30319\WPF\WindowsBase.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore">
      <HintPath>C:\Windows\Microsoft.NET\Framework\v4.0.30319\WPF\PresentationCore.dll</HintPath>
    </Reference>
    <Reference Include="PresentationFramework">
      <HintPath>C:\Windows\Microsoft.NET\Framework\v4.0.30319\WPF\PresentationFramework.dll</HintPath>
    </Reference>
    <Reference Include="UIAutomationProvider">
      <HintPath>C:\Windows\Microsoft.NET\Framework\v4.0.30319\WPF\UIAutomationProvider.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="System.ComponentModel.Annotations" Version="5.0.0" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Indicators\%40IchimokuCloud.cs" />
    <Compile Include="Indicators\%40FVG.cs" />
    <Compile Include="Resource.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resource.resx</DependentUpon>
    </Compile>
    <Compile Include="BarsTypes\%40DayBarsType.cs" />
    <Compile Include="BarsTypes\%40HeikenAshiBarsType.cs" />
    <Compile Include="BarsTypes\%40KagiBarsType.cs" />
    <Compile Include="BarsTypes\%40LineBreakBarsType.cs" />
    <Compile Include="BarsTypes\%40MinuteBarsType.cs" />
    <Compile Include="BarsTypes\%40MonthBarsType.cs" />
    <Compile Include="BarsTypes\%40PointAndFigureBarsType.cs" />
    <Compile Include="BarsTypes\%40RangeBarsType.cs" />
    <Compile Include="BarsTypes\%40RenkoBarsType.cs" />
    <Compile Include="BarsTypes\%40SecondBarsType.cs" />
    <Compile Include="BarsTypes\%40TickBarsType.cs" />
    <Compile Include="BarsTypes\%40VolumeBarsType.cs" />
    <Compile Include="BarsTypes\%40WeekBarsType.cs" />
    <Compile Include="BarsTypes\%40YearsBarsType.cs" />
    <Compile Include="ChartStyles\%40BoxStyle.cs" />
    <Compile Include="ChartStyles\%40Equivolume.cs" />
    <Compile Include="ChartStyles\%40HollowCandleStyle.cs" />
    <Compile Include="ChartStyles\%40CandleStyle.cs" />
    <Compile Include="ChartStyles\%40KagiStyle.cs" />
    <Compile Include="ChartStyles\%40LineOnCloseStyle.cs" />
    <Compile Include="ChartStyles\%40MountainStyle.cs" />
    <Compile Include="ChartStyles\%40OhlcStyle.cs" />
    <Compile Include="ChartStyles\%40OpenCloseStyle.cs" />
    <Compile Include="ChartStyles\%40PointAndFigureStyle.cs" />
    <Compile Include="DrawingTools\%40AndrewsPitchfork.cs" />
    <Compile Include="DrawingTools\%40Arc.cs" />
    <Compile Include="DrawingTools\%40ChartMarkers.cs" />
    <Compile Include="DrawingTools\%40PathTool.cs" />
    <Compile Include="DrawingTools\%40Polygon.cs" />
    <Compile Include="DrawingTools\%40Ruler.cs" />
    <Compile Include="DrawingTools\%40Text.cs" />
    <Compile Include="DrawingTools\%40FibonacciTools.cs" />
    <Compile Include="DrawingTools\%40GannFan.cs" />
    <Compile Include="DrawingTools\%40Lines.cs" />
    <Compile Include="DrawingTools\%40PriceLevels.cs" />
    <Compile Include="DrawingTools\%40Region.cs" />
    <Compile Include="DrawingTools\%40RegionHighlight.cs" />
    <Compile Include="DrawingTools\%40RegressionChannel.cs" />
    <Compile Include="DrawingTools\%40RiskReward.cs" />
    <Compile Include="DrawingTools\%40Shapes.cs" />
    <Compile Include="DrawingTools\%40TimeCycles.cs" />
    <Compile Include="DrawingTools\%40TrendChannel.cs" />
    <Compile Include="ImportTypes\%40TickDataImportType.cs" />
    <Compile Include="Indicators\%40BarTimer.cs" />
    <Compile Include="Indicators\%40Correlation.cs" />
    <Compile Include="Indicators\%40BlockVolume.cs" />
    <Compile Include="Indicators\%40BuySellVolume.cs" />
    <Compile Include="Indicators\%40CamarillaPivots.cs" />
    <Compile Include="Indicators\%40CandleStickPattern.cs" />
    <Compile Include="Indicators\%40ChoppinessIndex.cs" />
    <Compile Include="Indicators\%40COT.cs" />
    <Compile Include="Indicators\%40Darvas.cs" />
    <Compile Include="Indicators\%40DisparityIndex.cs" />
    <Compile Include="Indicators\%40FibonacciPivots.cs" />
    <Compile Include="Indicators\%40DrawingToolTile.cs" />
    <Compile Include="Indicators\%40McClellanOscillator.cs" />
    <Compile Include="Indicators\%40MoneyFlowOscillator.cs" />
    <Compile Include="Indicators\%40MovingAverageRibbon.cs" />
    <Compile Include="Indicators\%40NetChangeDisplay.cs" />
    <Compile Include="Indicators\%40Pivots.cs" />
    <Compile Include="Indicators\%40PriceLine.cs" />
    <Compile Include="Indicators\%40PsychologicalLine.cs" />
    <Compile Include="Indicators\%40RangeCounter.cs" />
    <Compile Include="Indicators\%40RelativeVigorIndex.cs" />
    <Compile Include="Indicators\%40SampleCustomRender.cs" />
    <Compile Include="Indicators\%40TickCounter.cs" />
    <Compile Include="Indicators\%40TrendLines.cs" />
    <Compile Include="Indicators\%40VolumeCounter.cs" />
    <Compile Include="Indicators\%40VolumeProfile.cs" />
    <Compile Include="Indicators\%40VolumeUpDown.cs" />
    <Compile Include="Indicators\%40VolumeZones.cs" />
    <Compile Include="Indicators\%40Vortex.cs" />
    <Compile Include="MarketAnalyzerColumns\%40ChartMini.cs" />
    <Compile Include="MarketAnalyzerColumns\%40ChartNetChange.cs" />
    <Compile Include="MarketAnalyzerColumns\%40DaysUntilRollover.cs" />
    <Compile Include="MarketAnalyzerColumns\%40NetChangeMaxDown.cs" />
    <Compile Include="MarketAnalyzerColumns\%40NetChangeMaxUp.cs" />
    <Compile Include="MarketAnalyzerColumns\%40TSTrend.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxStrength.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxStrengthLong.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxStrengthShort.cs" />
    <Compile Include="Optimizers\%40StrategyGenerator.cs" />
    <Compile Include="ResourceEnumConverter.cs" />
    <Compile Include="ImportTypes\%40TextImportType.cs" />
    <Compile Include="ImportTypes\%40TextImportTypeBeginningOfBar.cs" />
    <Compile Include="Indicators\%40ADX.cs" />
    <Compile Include="Indicators\%40Aroon.cs" />
    <Compile Include="Indicators\%40AroonOscillator.cs" />
    <Compile Include="Indicators\%40ATR.cs" />
    <Compile Include="Indicators\%40Bollinger.cs" />
    <Compile Include="Indicators\%40CCI.cs" />
    <Compile Include="Indicators\%40ChaikinMoneyFlow.cs" />
    <Compile Include="Indicators\%40ChaikinOscillator.cs" />
    <Compile Include="Indicators\%40CurrentDayOHL.cs" />
    <Compile Include="Indicators\%40DMI.cs" />
    <Compile Include="Indicators\%40DonchianChannel.cs" />
    <Compile Include="Indicators\%40EMA.cs" />
    <Compile Include="Indicators\%40FisherTransform.cs" />
    <Compile Include="Indicators\%40Indicator.cs" />
    <Compile Include="Indicators\%40KeltnerChannel.cs" />
    <Compile Include="Indicators\%40LinReg.cs" />
    <Compile Include="Indicators\%40MACD.cs" />
    <Compile Include="Indicators\%40MAMA.cs" />
    <Compile Include="Indicators\%40MAX.cs" />
    <Compile Include="Indicators\%40MFI.cs" />
    <Compile Include="Indicators\%40MIN.cs" />
    <Compile Include="Indicators\%40Momentum.cs" />
    <Compile Include="Indicators\%40OBV.cs" />
    <Compile Include="Indicators\%40PriceOscillator.cs" />
    <Compile Include="Indicators\%40PriorDayOHLC.cs" />
    <Compile Include="Indicators\%40ROC.cs" />
    <Compile Include="Indicators\%40RSI.cs" />
    <Compile Include="Indicators\%40SMA.cs" />
    <Compile Include="Indicators\%40StdDev.cs" />
    <Compile Include="Indicators\%40Stochastics.cs" />
    <Compile Include="Indicators\%40StochasticsFast.cs" />
    <Compile Include="Indicators\%40SUM.cs" />
    <Compile Include="Indicators\%40Swing.cs" />
    <Compile Include="Indicators\%40TMA.cs" />
    <Compile Include="Indicators\%40TSI.cs" />
    <Compile Include="Indicators\%40VOL.cs" />
    <Compile Include="Indicators\%40WilliamsR.cs" />
    <Compile Include="Indicators\%40WMA.cs" />
    <Compile Include="Indicators\%40ADL.cs" />
    <Compile Include="Indicators\%40ADXR.cs" />
    <Compile Include="Indicators\%40BOP.cs" />
    <Compile Include="Indicators\%40Range.cs" />
    <Compile Include="Indicators\%40APZ.cs" />
    <Compile Include="Indicators\%40ChaikinVolatility.cs" />
    <Compile Include="Indicators\%40CMO.cs" />
    <Compile Include="Indicators\%40ConstantLines.cs" />
    <Compile Include="Indicators\%40DEMA.cs" />
    <Compile Include="Indicators\%40DM.cs" />
    <Compile Include="Indicators\%40DoubleStochastics.cs" />
    <Compile Include="Indicators\%40EaseOfMovement.cs" />
    <Compile Include="Indicators\%40TSF.cs" />
    <Compile Include="Indicators\%40DMIndex.cs" />
    <Compile Include="Indicators\%40FOSC.cs" />
    <Compile Include="Indicators\%40HMA.cs" />
    <Compile Include="Indicators\%40KAMA.cs" />
    <Compile Include="Indicators\%40KeyReversalDown.cs" />
    <Compile Include="Indicators\%40KeyReversalUp.cs" />
    <Compile Include="Indicators\%40LinRegIntercept.cs" />
    <Compile Include="Indicators\%40LinRegSlope.cs" />
    <Compile Include="Indicators\%40NBarsDown.cs" />
    <Compile Include="Indicators\%40NBarsUp.cs" />
    <Compile Include="Indicators\%40TEMA.cs" />
    <Compile Include="Indicators\%40MAEnvelopes.cs" />
    <Compile Include="Indicators\%40ParabolicSAR.cs" />
    <Compile Include="Indicators\%40PFE.cs" />
    <Compile Include="Indicators\%40PPO.cs" />
    <Compile Include="Indicators\%40RegressionChannel.cs" />
    <Compile Include="Indicators\%40RIND.cs" />
    <Compile Include="Indicators\%40RSquared.cs" />
    <Compile Include="Indicators\%40RSS.cs" />
    <Compile Include="Indicators\%40RVI.cs" />
    <Compile Include="Indicators\%40StdError.cs" />
    <Compile Include="Indicators\%40StochRSI.cs" />
    <Compile Include="Indicators\%40T3.cs" />
    <Compile Include="Indicators\%40TRIX.cs" />
    <Compile Include="Indicators\%40UltimateOscillator.cs" />
    <Compile Include="Indicators\%40VMA.cs" />
    <Compile Include="Indicators\%40VOLMA.cs" />
    <Compile Include="Indicators\%40VolumeOscillator.cs" />
    <Compile Include="Indicators\%40VROC.cs" />
    <Compile Include="Indicators\%40VWMA.cs" />
    <Compile Include="Indicators\%40ZLEMA.cs" />
    <Compile Include="Indicators\%40BuySellPressure.cs" />
    <Compile Include="Indicators\%40ZigZag.cs" />
    <Compile Include="AssemblyInfo.cs" />
    <Compile Include="MarketAnalyzerColumns\%40AskPrice.cs" />
    <Compile Include="MarketAnalyzerColumns\%40AskSize.cs" />
    <Compile Include="MarketAnalyzerColumns\%40AverageDailyVolume.cs" />
    <Compile Include="MarketAnalyzerColumns\%40Beta.cs" />
    <Compile Include="MarketAnalyzerColumns\%40BidAskSpread.cs" />
    <Compile Include="MarketAnalyzerColumns\%40BidPrice.cs" />
    <Compile Include="MarketAnalyzerColumns\%40BidSize.cs" />
    <Compile Include="MarketAnalyzerColumns\%40CalendarYearHigh.cs" />
    <Compile Include="MarketAnalyzerColumns\%40CalendarYearHighDate.cs" />
    <Compile Include="MarketAnalyzerColumns\%40CalendarYearLow.cs" />
    <Compile Include="MarketAnalyzerColumns\%40CalendarYearLowDate.cs" />
    <Compile Include="MarketAnalyzerColumns\%40CurrentRatio.cs" />
    <Compile Include="MarketAnalyzerColumns\%40DailyHigh.cs" />
    <Compile Include="MarketAnalyzerColumns\%40DailyLow.cs" />
    <Compile Include="MarketAnalyzerColumns\%40DailyVolume.cs" />
    <Compile Include="MarketAnalyzerColumns\%40Description.cs" />
    <Compile Include="MarketAnalyzerColumns\%40DividendAmount.cs" />
    <Compile Include="MarketAnalyzerColumns\%40DividendPayDate.cs" />
    <Compile Include="MarketAnalyzerColumns\%40DividendYield.cs" />
    <Compile Include="MarketAnalyzerColumns\%40EarningsPerShare.cs" />
    <Compile Include="MarketAnalyzerColumns\%40FiveYearsGrowthPercentage.cs" />
    <Compile Include="MarketAnalyzerColumns\%40High52Weeks.cs" />
    <Compile Include="MarketAnalyzerColumns\%40High52WeeksDate.cs" />
    <Compile Include="MarketAnalyzerColumns\%40HistoricalVolatility.cs" />
    <Compile Include="MarketAnalyzerColumns\%40Instrument.cs" />
    <Compile Include="MarketAnalyzerColumns\%40LastClose.cs" />
    <Compile Include="MarketAnalyzerColumns\%40LastPrice.cs" />
    <Compile Include="MarketAnalyzerColumns\%40LastSize.cs" />
    <Compile Include="MarketAnalyzerColumns\%40Low52Weeks.cs" />
    <Compile Include="MarketAnalyzerColumns\%40Low52WeeksDate.cs" />
    <Compile Include="MarketAnalyzerColumns\%40MarketAnalyzerColumn.cs" />
    <Compile Include="MarketAnalyzerColumns\%40MarketCap.cs" />
    <Compile Include="MarketAnalyzerColumns\%40NetChange.cs" />
    <Compile Include="MarketAnalyzerColumns\%40NextYearsEarningsPerShare.cs" />
    <Compile Include="MarketAnalyzerColumns\%40Notes.cs" />
    <Compile Include="MarketAnalyzerColumns\%40Opening.cs" />
    <Compile Include="MarketAnalyzerColumns\%40OpenInterest.cs" />
    <Compile Include="MarketAnalyzerColumns\%40PercentHeldByInstitutions.cs" />
    <Compile Include="MarketAnalyzerColumns\%40PositionAvgPrice.cs" />
    <Compile Include="MarketAnalyzerColumns\%40PositionSize.cs" />
    <Compile Include="MarketAnalyzerColumns\%40PriceEarningsRatio.cs" />
    <Compile Include="MarketAnalyzerColumns\%40ProfitLoss.cs" />
    <Compile Include="MarketAnalyzerColumns\%40RealizedProfitLoss.cs" />
    <Compile Include="MarketAnalyzerColumns\%40RevenuePerShare.cs" />
    <Compile Include="MarketAnalyzerColumns\%40Settlement.cs" />
    <Compile Include="MarketAnalyzerColumns\%40SharesOutstanding.cs" />
    <Compile Include="MarketAnalyzerColumns\%40ShortInterest.cs" />
    <Compile Include="MarketAnalyzerColumns\%40ShortInterestRatio.cs" />
    <Compile Include="MarketAnalyzerColumns\%40TimeLastTick.cs" />
    <Compile Include="MarketAnalyzerColumns\%40TradedContracts.cs" />
    <Compile Include="MarketAnalyzerColumns\%40UnrealizedProfitLoss.cs" />
    <Compile Include="MarketAnalyzerColumns\%40VWAP.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxAvgMfe.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxAvgMfeLong.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxAvgMfeShort.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxAvgProfit.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxAvgProfitLong.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxAvgProfitShort.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxNetProfit.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxNetProfitLong.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxNetProfitShort.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxPercentProfitable.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxPercentProfitableLong.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxPercentProfitableShort.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxProbability.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxProbabilityLong.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxProbabilityShort.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxProfitFactor.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxProfitFactorLong.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxProfitFactorShort.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxR2.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxR2Long.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxR2Short.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxSharpeRatio.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxSharpeRatioLong.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxSharpeRatioShort.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxSortinoRatio.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxSortinoRatioLong.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxSortinoRatioShort.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxUlcerRatio.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxUlcerRatioLong.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxUlcerRatioShort.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxWinLossRatio.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxWinLossRatioLong.cs" />
    <Compile Include="OptimizationFitnesses\%40MaxWinLossRatioShort.cs" />
    <Compile Include="OptimizationFitnesses\%40MinAvgMae.cs" />
    <Compile Include="OptimizationFitnesses\%40MinAvgMaeLong.cs" />
    <Compile Include="OptimizationFitnesses\%40MinAvgMaeShort.cs" />
    <Compile Include="OptimizationFitnesses\%40MinDrawDown.cs" />
    <Compile Include="OptimizationFitnesses\%40MinDrawDownLong.cs" />
    <Compile Include="OptimizationFitnesses\%40MinDrawDownShort.cs" />
    <Compile Include="Optimizers\%40DefaultOptimizer.cs" />
    <Compile Include="Optimizers\%40GeneticOptimizer.cs" />
    <Compile Include="PerformanceMetrics\%40PerformanceMetric.cs" />
    <Compile Include="PerformanceMetrics\%40SampleCumProfit.cs" />
    <Compile Include="ShareServices\%40Mail.cs" />
    <Compile Include="ShareServices\%40StockTwits.cs" />
    <Compile Include="ShareServices\%40TextMessage.cs" />
    <Compile Include="ShareServices\%40Twitter.cs" />
    <Compile Include="Strategies\%40SampleAtmStrategy.cs" />
    <Compile Include="Strategies\%40SampleMACrossOver.cs" />
    <Compile Include="Strategies\%40SampleMultiInstrument.cs" />
    <Compile Include="Strategies\%40SampleMultiTimeFrame.cs" />
    <Compile Include="Strategies\%40Strategy.cs" />
    <Compile Include="SuperDomColumns\%40APQ.cs" />
    <Compile Include="SuperDomColumns\%40Notes.cs" />
    <Compile Include="SuperDomColumns\%40ProfitLoss.cs" />
    <Compile Include="SuperDomColumns\%40Recent.cs" />
    <Compile Include="SuperDomColumns\%40Volume.cs" />
    <Compile Include="SuperDomColumns\%40PullingStacking.cs" />
    <Compile Include="AddOns\%40%40MyCustomAddOn.cs" />
    <Compile Include="BarsTypes\RenkoComp.cs" />
    <Compile Include="BarsTypes\rgVolatilityRenko.cs" />
    <Compile Include="Indicators\PropTraderz\GoldenSetupV3_1.cs" />
    <Compile Include="Indicators\PropTraderz\PtzGLManager.cs" />
    <Compile Include="Indicators\TG\BarInfo.cs" />
    <Compile Include="Indicators\VOLAREIndicators\JTNC_MovAvgTrend.cs" />
    <Compile Include="Indicators\VOLAREIndicators\SharedPOC.cs" />
    <Compile Include="Indicators\VOLAREIndicators\StraddlePOCSignal.cs" />
    <Compile Include="Indicators\ATR_Direct.cs" />
    <Compile Include="Indicators\B4Indicator.cs" />
    <Compile Include="Indicators\DrawVLinesCSV.cs" />
    <Compile Include="Indicators\FiftyLines.cs" />
    <Compile Include="Indicators\LabeledHorizontalLines.cs" />
    <Compile Include="Indicators\OgresBarTimer.cs" />
    <Compile Include="Indicators\OrderLineDecorator.cs" />
    <Compile Include="Indicators\rgMovAves.cs" />
    <Compile Include="Indicators\rgStochastics.cs" />
    <Compile Include="Indicators\SimplePOC.cs" />
    <Compile Include="Indicators\TimeZoneColors.cs" />
    <Compile Include="Strategies\TradeSaberStrategies\OrderEntryButtons.cs" />
    <Compile Include="Strategies\AdoptAccountPositionTestUnManaged.cs" />
    <Compile Include="Strategies\DerkUtils.cs" />
    <Compile Include="Strategies\EAPvtTest02F.cs" />
    <Compile Include="Strategies\FiftyLevel.cs" />
    <Compile Include="Strategies\Ladder.cs" />
    <Compile Include="Strategies\LadderURL.cs" />
    <Compile Include="Strategies\LogSample.cs" />
    <Compile Include="Strategies\Obsidian.cs" />
    <Compile Include="Strategies\Onyx.cs" />
    <Compile Include="Strategies\POCStraddle.cs" />
    <Compile Include="Strategies\Razor.cs" />
    <Compile Include="Strategies\RazorLight.cs" />
    <Compile Include="Strategies\RazorURL.cs" />
    <Compile Include="Strategies\SampleIntrabarBacktest.cs" />
    <Compile Include="Strategies\SampleScaleOut.cs" />
    <Compile Include="Strategies\StochasticsFastStrategy_v8.cs" />
    <Compile Include="Strategies\StraddlePOC1.cs" />
    <Compile Include="Strategies\StraddlePOCStrategy.cs" />
    <Compile Include="Strategies\StraddlePOCStrategy1.cs" />
    <Compile Include="Strategies\TepVDS.cs" />
    <Compile Include="Strategies\UnmanagedTemplate.cs" />
    <Compile Include="obj\x64\Debug\.NETFramework,Version=v4.8.AssemblyAttributes.cs" />
    <Compile Include="NinZaRenko_v5.1_NT8.cs" />
    <Compile Include="Strategies\StochFastADX.cs" />
    <Compile Include="PowerBundle360AIv1.03.cs" />
    <Compile Include="Strategies\Strike123Mngd.cs" />
    <Compile Include="Strategies\Strike123SB.cs" />
    <Compile Include="Strategies\Strike123SBview.cs" />
    <Compile Include="Strategies\Shrike123test.cs" />
    <Compile Include="Indicators\ApexTrailDD.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Update="Resource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Resource.Designer.cs</LastGenOutput>
      <CustomToolNamespace>NinjaTrader.Custom</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="Resource.ru-RU.resx">
      <DependentUpon>Resource.resx</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Update="Resource.fr-FR.resx">
      <DependentUpon>Resource.resx</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Update="Resource.de-DE.resx">
      <DependentUpon>Resource.resx</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Update="Resource.pt-PT.resx">
      <DependentUpon>Resource.resx</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Update="Resource.es-ES.resx">
      <DependentUpon>Resource.resx</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Update="Resource.zh-Hans.resx">
      <DependentUpon>Resource.resx</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Update="Resource.it-IT.resx">
      <DependentUpon>Resource.resx</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Update="Resource.ko-KR.resx">
      <DependentUpon>Resource.resx</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Remove="obj\**" />
    <Page Remove="obj\**" />
  </ItemGroup>
</Project>